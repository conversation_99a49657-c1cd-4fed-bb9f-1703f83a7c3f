/* Custom CSS for Course Player */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Dark theme variables */
[data-bs-theme="dark"] {
    --bs-body-bg: #1a1a1a;
    --bs-body-color: #ffffff;
    --bs-card-bg: #2d2d2d;
    --bs-border-color: #404040;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    transition: all 0.3s ease;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

/* Course List */
.course-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-left: 4px solid transparent;
}

.course-item:hover {
    background-color: var(--bs-light);
    border-left-color: var(--primary-color);
}

.course-item.active {
    background-color: var(--primary-color);
    color: white;
    border-left-color: var(--primary-color);
}

[data-bs-theme="dark"] .course-item:hover {
    background-color: var(--bs-dark);
}

/* Video Player */
.video-js {
    border-radius: 0.375rem;
}

.video-js .vjs-big-play-button {
    border-radius: 50%;
    background-color: rgba(13, 110, 253, 0.8);
    border: none;
    font-size: 2.5em;
}

.video-js .vjs-control-bar {
    background: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.7));
}

/* Module Cards */
.module-card {
    margin-bottom: 1.5rem;
}

.module-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 0.375rem 0.375rem 0 0;
}

.video-item {
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.video-item:hover {
    background-color: var(--bs-light);
    border-left-color: var(--primary-color);
}

.video-item.active {
    background-color: rgba(13, 110, 253, 0.1);
    border-left-color: var(--primary-color);
}

[data-bs-theme="dark"] .video-item:hover {
    background-color: var(--bs-dark);
}

.video-thumbnail {
    width: 80px;
    height: 45px;
    object-fit: cover;
    border-radius: 0.25rem;
}

.video-duration {
    position: absolute;
    bottom: 2px;
    right: 2px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 0.7rem;
}

/* Progress Bars */
.progress-bar-video {
    height: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.progress-container {
    height: 4px;
    background-color: var(--bs-border-color);
    border-radius: 2px;
    overflow: hidden;
}

/* Stats */
.stat-item {
    padding: 0.5rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--bs-secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Bookmarks and Notes */
.bookmark-item, .note-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.bookmark-item:hover, .note-item:hover {
    background-color: var(--bs-light);
}

[data-bs-theme="dark"] .bookmark-item:hover,
[data-bs-theme="dark"] .note-item:hover {
    background-color: var(--bs-dark);
}

.timestamp-badge {
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
}

/* Search Results */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

[data-bs-theme="dark"] .search-results {
    background: var(--bs-dark);
    border-color: var(--bs-border-color);
}

.search-result-item {
    padding: 0.75rem;
    border-bottom: 1px solid var(--bs-border-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-result-item:hover {
    background-color: var(--bs-light);
}

[data-bs-theme="dark"] .search-result-item:hover {
    background-color: var(--bs-secondary);
}

.search-result-item:last-child {
    border-bottom: none;
}

/* Speed Control Buttons */
.btn-group .btn[data-speed] {
    min-width: 50px;
}

.btn-group .btn[data-speed].active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .col-md-3 {
        margin-bottom: 2rem;
    }
    
    .video-thumbnail {
        width: 60px;
        height: 34px;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .stat-number {
        font-size: 1.25rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bs-light);
}

::-webkit-scrollbar-thumb {
    background: var(--bs-secondary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

[data-bs-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bs-dark);
}

[data-bs-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--bs-secondary);
}

/* Toast Notifications */
.toast {
    min-width: 300px;
}

.toast-success {
    border-left: 4px solid var(--success-color);
}

.toast-error {
    border-left: 4px solid var(--danger-color);
}

.toast-info {
    border-left: 4px solid var(--info-color);
}

/* Welcome Section */
#welcomeSection {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Course Header */
.course-stats .badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
}

/* Video Controls */
.video-controls {
    background: var(--bs-light);
    border-radius: 0.375rem;
    padding: 1rem;
}

[data-bs-theme="dark"] .video-controls {
    background: var(--bs-dark);
}
