// Global Application JavaScript

class CoursePlayer {
    constructor() {
        this.currentCourse = null;
        this.currentVideo = null;
        this.player = null;
        this.preferences = {};
        this.searchTimeout = null;
        
        this.init();
    }
    
    init() {
        this.loadPreferences();
        this.setupEventListeners();
        this.loadCourses();
        this.setupTheme();
    }
    
    setupEventListeners() {
        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });
        
        // Global search
        const searchInput = document.getElementById('globalSearch');
        searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.performSearch(e.target.value);
            }, 300);
        });
        
        // Settings
        document.getElementById('saveSettings').addEventListener('click', () => {
            this.saveSettings();
        });
        
        // Directory scanner
        document.getElementById('startScan').addEventListener('click', () => {
            this.scanDirectory();
        });

        // Directory picker
        document.getElementById('browseDirectoryBtn').addEventListener('click', () => {
            this.browseDirectory();
        });

        // Directory path input change
        document.getElementById('directoryPath').addEventListener('input', (e) => {
            this.handleDirectoryPathChange(e.target.value);
        });

        // Reset modal when closed
        document.getElementById('scanDirectoryModal').addEventListener('hidden.bs.modal', () => {
            this.resetScanModal();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }
    
    async loadPreferences() {
        try {
            const response = await fetch('/api/preferences');
            const data = await response.json();
            this.preferences = data.preferences;
            this.applyPreferences();
        } catch (error) {
            console.error('Error loading preferences:', error);
            this.preferences = {
                theme: 'light',
                playback_speed: 1.0,
                auto_play_next: true,
                volume: 0.8,
                quality: 'auto'
            };
        }
    }
    
    applyPreferences() {
        // Apply theme
        document.body.setAttribute('data-bs-theme', this.preferences.theme);
        
        // Update settings form
        document.getElementById('playbackSpeed').value = this.preferences.playback_speed;
        document.getElementById('volume').value = this.preferences.volume;
        document.getElementById('autoPlayNext').checked = this.preferences.auto_play_next;
    }
    
    setupTheme() {
        const themeIcon = document.getElementById('themeIcon');
        const currentTheme = this.preferences.theme || 'light';
        
        if (currentTheme === 'dark') {
            themeIcon.className = 'bi bi-moon-fill';
        } else {
            themeIcon.className = 'bi bi-sun-fill';
        }
    }
    
    toggleTheme() {
        const currentTheme = document.body.getAttribute('data-bs-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.body.setAttribute('data-bs-theme', newTheme);
        this.preferences.theme = newTheme;
        
        const themeIcon = document.getElementById('themeIcon');
        themeIcon.className = newTheme === 'dark' ? 'bi bi-moon-fill' : 'bi bi-sun-fill';
        
        this.savePreferences();
    }
    
    async savePreferences() {
        try {
            await fetch('/api/preferences', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.preferences)
            });
        } catch (error) {
            console.error('Error saving preferences:', error);
        }
    }
    
    async saveSettings() {
        const playbackSpeed = parseFloat(document.getElementById('playbackSpeed').value);
        const volume = parseFloat(document.getElementById('volume').value);
        const autoPlayNext = document.getElementById('autoPlayNext').checked;
        
        this.preferences.playback_speed = playbackSpeed;
        this.preferences.volume = volume;
        this.preferences.auto_play_next = autoPlayNext;
        
        await this.savePreferences();
        
        // Apply to current player if exists
        if (this.player) {
            this.player.playbackRates([0.5, 0.75, 1, 1.25, 1.5, 2]);
            this.player.playbackRate(playbackSpeed);
            this.player.volume(volume);
        }
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
        modal.hide();
        
        this.showToast('Settings saved successfully', 'success');
    }
    
    async loadCourses() {
        try {
            const response = await fetch('/api/courses');
            const courses = await response.json();
            
            this.renderCourses(courses);
            this.updateStats(courses);
            
            if (courses.length === 0) {
                document.getElementById('welcomeSection').classList.remove('d-none');
                document.getElementById('courseContent').classList.add('d-none');
            }
        } catch (error) {
            console.error('Error loading courses:', error);
            this.showToast('Error loading courses', 'error');
        }
    }
    
    renderCourses(courses) {
        const coursesList = document.getElementById('coursesList');
        coursesList.innerHTML = '';
        
        if (courses.length === 0) {
            coursesList.innerHTML = `
                <div class="list-group-item text-center text-muted">
                    <i class="bi bi-folder-x display-6"></i>
                    <p class="mt-2 mb-0">No courses found</p>
                    <small>Add a course directory to get started</small>
                </div>
            `;
            return;
        }
        
        courses.forEach(course => {
            const courseItem = document.createElement('div');
            courseItem.className = 'list-group-item course-item';
            courseItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${course.name}</h6>
                        <small class="text-muted">${course.modules_count} modules</small>
                    </div>
                    <i class="bi bi-play-circle text-primary"></i>
                </div>
            `;
            
            courseItem.addEventListener('click', () => {
                this.loadCourse(course.id);
            });
            
            coursesList.appendChild(courseItem);
        });
    }
    
    updateStats(courses) {
        const totalCourses = courses.length;
        let totalVideos = 0;
        
        courses.forEach(course => {
            // This would need to be calculated properly from modules/videos
            totalVideos += course.modules_count * 5; // Rough estimate
        });
        
        document.getElementById('totalCourses').textContent = totalCourses;
        document.getElementById('totalVideos').textContent = totalVideos;
    }
    
    showToast(message, type = 'info') {
        const toast = document.getElementById('notificationToast');
        const toastMessage = document.getElementById('toastMessage');
        
        toastMessage.textContent = message;
        toast.className = `toast toast-${type}`;
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }
    
    handleKeyboardShortcuts(e) {
        // Only handle shortcuts when video player is active
        if (!this.player) return;
        
        switch(e.code) {
            case 'Space':
                e.preventDefault();
                if (this.player.paused()) {
                    this.player.play();
                } else {
                    this.player.pause();
                }
                break;
            case 'ArrowLeft':
                e.preventDefault();
                this.player.currentTime(this.player.currentTime() - 10);
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.player.currentTime(this.player.currentTime() + 10);
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.player.volume(Math.min(1, this.player.volume() + 0.1));
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.player.volume(Math.max(0, this.player.volume() - 0.1));
                break;
        }
    }
    
    formatDuration(seconds) {
        if (!seconds) return '0:00';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }
    
    formatFileSize(bytes) {
        if (!bytes) return '0 B';

        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));

        return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
    }

    async performSearch(query) {
        if (!query.trim()) {
            this.hideSearchResults();
            return;
        }

        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
            const results = await response.json();

            this.showSearchResults(results);
        } catch (error) {
            console.error('Error performing search:', error);
        }
    }

    showSearchResults(results) {
        // Remove existing search results
        this.hideSearchResults();

        const searchInput = document.getElementById('globalSearch');
        const searchContainer = searchInput.parentElement;

        const resultsDiv = document.createElement('div');
        resultsDiv.className = 'search-results';
        resultsDiv.id = 'searchResults';

        let hasResults = false;

        // Add courses
        if (results.courses && results.courses.length > 0) {
            hasResults = true;
            results.courses.forEach(course => {
                const item = document.createElement('div');
                item.className = 'search-result-item';
                item.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-folder-fill text-primary me-2"></i>
                        <div>
                            <div class="fw-bold">${course.name}</div>
                            <small class="text-muted">Course • ${course.modules_count} modules</small>
                        </div>
                    </div>
                `;
                item.addEventListener('click', () => {
                    this.loadCourse(course.id);
                    this.hideSearchResults();
                    searchInput.value = '';
                });
                resultsDiv.appendChild(item);
            });
        }

        // Add videos
        if (results.videos && results.videos.length > 0) {
            hasResults = true;
            results.videos.forEach(video => {
                const item = document.createElement('div');
                item.className = 'search-result-item';
                item.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-play-circle-fill text-success me-2"></i>
                        <div>
                            <div class="fw-bold">${video.name}</div>
                            <small class="text-muted">Video • ${this.formatDuration(video.duration)}</small>
                        </div>
                    </div>
                `;
                item.addEventListener('click', () => {
                    // This would need to load the course and play the specific video
                    this.hideSearchResults();
                    searchInput.value = '';
                });
                resultsDiv.appendChild(item);
            });
        }

        if (!hasResults) {
            const item = document.createElement('div');
            item.className = 'search-result-item text-center text-muted';
            item.innerHTML = 'No results found';
            resultsDiv.appendChild(item);
        }

        searchContainer.appendChild(resultsDiv);
    }

    hideSearchResults() {
        const existingResults = document.getElementById('searchResults');
        if (existingResults) {
            existingResults.remove();
        }
    }

    async browseDirectory() {
        try {
            // Check if the File System Access API is supported
            if ('showDirectoryPicker' in window) {
                const directoryHandle = await window.showDirectoryPicker();
                const directoryPath = directoryHandle.name;

                // Update the UI with selected directory
                this.updateSelectedDirectory(directoryPath, directoryHandle);

                // Preview directory contents
                await this.previewDirectory(directoryHandle);

            } else {
                // Fallback for browsers that don't support File System Access API
                this.showDirectoryPickerFallback();
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('Error selecting directory:', error);
                this.showToast('Error selecting directory. Please try entering the path manually.', 'error');
            }
        }
    }

    showDirectoryPickerFallback() {
        // Show a message about browser compatibility and focus on manual input
        this.showToast('Directory picker not supported in this browser. Please enter the path manually below.', 'info');

        // Focus on the manual input field
        document.getElementById('directoryPath').focus();

        // Highlight the manual input card
        const manualCard = document.querySelector('#scanDirectoryModal .card.border-secondary');
        manualCard.classList.add('border-primary');
        manualCard.classList.remove('border-secondary');

        setTimeout(() => {
            manualCard.classList.remove('border-primary');
            manualCard.classList.add('border-secondary');
        }, 3000);
    }

    updateSelectedDirectory(directoryPath, directoryHandle = null) {
        const selectedInfo = document.getElementById('selectedDirectoryInfo');
        const selectedPath = document.getElementById('selectedDirectoryPath');
        const directoryInput = document.getElementById('directoryPath');

        selectedPath.textContent = directoryPath;
        selectedInfo.classList.remove('d-none');

        // Also update the manual input field
        directoryInput.value = directoryPath;

        // Store the directory handle for later use
        if (directoryHandle) {
            this.selectedDirectoryHandle = directoryHandle;
        }
    }

    async previewDirectory(directoryHandle) {
        try {
            const preview = document.getElementById('directoryPreview');
            const contents = document.getElementById('directoryContents');

            contents.innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> Loading...</div>';
            preview.classList.remove('d-none');

            const items = [];
            let videoCount = 0;
            let folderCount = 0;

            // Read directory contents
            for await (const [name, handle] of directoryHandle.entries()) {
                if (handle.kind === 'directory') {
                    items.push({ name, type: 'folder' });
                    folderCount++;
                } else if (handle.kind === 'file') {
                    const extension = name.split('.').pop().toLowerCase();
                    if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v'].includes(extension)) {
                        items.push({ name, type: 'video' });
                        videoCount++;
                    }
                }

                // Limit preview to first 20 items
                if (items.length >= 20) break;
            }

            // Sort items: folders first, then videos
            items.sort((a, b) => {
                if (a.type !== b.type) {
                    return a.type === 'folder' ? -1 : 1;
                }
                return a.name.localeCompare(b.name);
            });

            // Display preview
            if (items.length === 0) {
                contents.innerHTML = '<div class="text-center text-muted">No video files or folders found</div>';
            } else {
                contents.innerHTML = `
                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            Found ${folderCount} folder(s) and ${videoCount} video file(s)
                            ${items.length >= 20 ? ' (showing first 20)' : ''}
                        </small>
                    </div>
                    ${items.map(item => `
                        <div class="directory-item ${item.type}">
                            <i class="bi bi-${item.type === 'folder' ? 'folder-fill' : 'play-circle-fill'}"></i>
                            <span>${item.name}</span>
                        </div>
                    `).join('')}
                `;
            }

        } catch (error) {
            console.error('Error previewing directory:', error);
            document.getElementById('directoryContents').innerHTML =
                '<div class="text-center text-danger">Error loading directory preview</div>';
        }
    }

    handleDirectoryPathChange(path) {
        const directoryInput = document.getElementById('directoryPath');
        const startScanBtn = document.getElementById('startScan');

        // Clear selected directory info when manual path is changed
        if (path.trim() === '') {
            document.getElementById('selectedDirectoryInfo').classList.add('d-none');
            document.getElementById('directoryPreview').classList.add('d-none');
            this.selectedDirectoryHandle = null;
            startScanBtn.disabled = true;
            directoryInput.classList.remove('is-valid', 'is-invalid');
            return;
        }

        // Validate directory path format
        const isValidPath = this.validateDirectoryPath(path);

        if (isValidPath) {
            directoryInput.classList.remove('is-invalid');
            directoryInput.classList.add('is-valid');
            startScanBtn.disabled = false;
        } else {
            directoryInput.classList.remove('is-valid');
            directoryInput.classList.add('is-invalid');
            startScanBtn.disabled = true;
        }
    }

    validateDirectoryPath(path) {
        if (!path || path.trim().length === 0) {
            return false;
        }

        // Basic path validation
        const trimmedPath = path.trim();

        // Check for invalid characters (basic validation)
        const invalidChars = /[<>"|?*]/;
        if (invalidChars.test(trimmedPath)) {
            return false;
        }

        // Check minimum length
        if (trimmedPath.length < 2) {
            return false;
        }

        // Check for common path patterns
        const windowsPath = /^[a-zA-Z]:\\/.test(trimmedPath);
        const unixPath = /^\//.test(trimmedPath) || /^~\//.test(trimmedPath);
        const relativePath = /^\.\.?\//.test(trimmedPath);

        return windowsPath || unixPath || relativePath || trimmedPath.includes('/') || trimmedPath.includes('\\');
    }

    async scanDirectory() {
        const directoryPath = document.getElementById('directoryPath').value.trim();

        // Validate input
        if (!directoryPath) {
            this.showToast('Please select a directory or enter a path', 'error');
            return;
        }

        if (!this.validateDirectoryPath(directoryPath)) {
            this.showToast('Please enter a valid directory path', 'error');
            return;
        }

        // Hide directory preview and selection info
        document.getElementById('directoryPreview').classList.add('d-none');
        document.getElementById('selectedDirectoryInfo').classList.add('d-none');

        // Show progress
        document.getElementById('scanProgress').classList.remove('d-none');
        document.getElementById('scanResults').classList.add('d-none');
        document.getElementById('startScan').disabled = true;

        try {
            const response = await fetch('/api/scan-directory', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    path: directoryPath
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            // Hide progress and show results
            document.getElementById('scanProgress').classList.add('d-none');
            document.getElementById('scanResults').classList.remove('d-none');

            // Update the enhanced results display
            document.getElementById('coursesAdded').textContent = result.courses_added || 0;
            document.getElementById('modulesAdded').textContent = result.modules_added || 0;
            document.getElementById('videosAdded').textContent = result.videos_added || 0;

            const resultsList = document.getElementById('scanResultsList');
            let resultsHtml = '';

            if (result.errors && result.errors.length > 0) {
                resultsHtml += '<div class="alert alert-warning"><strong>Warnings:</strong><ul class="mb-0">';
                result.errors.forEach(error => {
                    resultsHtml += `<li>${this.escapeHtml(error)}</li>`;
                });
                resultsHtml += '</ul></div>';
            }

            if (result.courses_added === 0 && result.modules_added === 0 && result.videos_added === 0) {
                resultsHtml += '<div class="alert alert-info"><i class="bi bi-info-circle me-2"></i>No new content was found in the specified directory. The directory may be empty or already scanned.</div>';
            } else {
                resultsHtml += '<div class="alert alert-success"><i class="bi bi-check-circle me-2"></i>Successfully added content to your library!</div>';
            }

            resultsList.innerHTML = resultsHtml;

            // Reload courses
            await this.loadCourses();

            this.showToast('Directory scan completed successfully', 'success');

        } catch (error) {
            console.error('Error scanning directory:', error);

            // Show user-friendly error messages
            let errorMessage = 'Error scanning directory. ';

            if (error.message.includes('404')) {
                errorMessage += 'Directory not found. Please check the path.';
            } else if (error.message.includes('403')) {
                errorMessage += 'Access denied. Please check directory permissions.';
            } else if (error.message.includes('Network')) {
                errorMessage += 'Network error. Please check your connection.';
            } else if (error.message.includes('timeout')) {
                errorMessage += 'Request timed out. The directory may be too large.';
            } else {
                errorMessage += 'Please check the path and try again.';
            }

            this.showToast(errorMessage, 'error');

            // Show error in results section
            document.getElementById('scanProgress').classList.add('d-none');
            document.getElementById('scanResults').classList.remove('d-none');

            document.getElementById('coursesAdded').textContent = '0';
            document.getElementById('modulesAdded').textContent = '0';
            document.getElementById('videosAdded').textContent = '0';

            document.getElementById('scanResultsList').innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Scan Failed:</strong> ${this.escapeHtml(errorMessage)}
                </div>
            `;

        } finally {
            document.getElementById('startScan').disabled = false;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Reset modal when it's closed
    resetScanModal() {
        const directoryInput = document.getElementById('directoryPath');
        const startScanBtn = document.getElementById('startScan');

        directoryInput.value = '';
        directoryInput.classList.remove('is-valid', 'is-invalid');
        document.getElementById('selectedDirectoryInfo').classList.add('d-none');
        document.getElementById('directoryPreview').classList.add('d-none');
        document.getElementById('scanProgress').classList.add('d-none');
        document.getElementById('scanResults').classList.add('d-none');
        startScanBtn.disabled = true;
        this.selectedDirectoryHandle = null;

        // Reset card highlighting
        const manualCard = document.querySelector('#scanDirectoryModal .card.border-secondary');
        if (manualCard) {
            manualCard.classList.remove('border-primary');
            manualCard.classList.add('border-secondary');
        }
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    window.coursePlayer = new CoursePlayer();
});
