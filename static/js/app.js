// Global Application JavaScript

class CoursePlayer {
    constructor() {
        this.currentCourse = null;
        this.currentVideo = null;
        this.player = null;
        this.preferences = {};
        this.searchTimeout = null;
        
        this.init();
    }
    
    init() {
        this.loadPreferences();
        this.setupEventListeners();
        this.loadCourses();
        this.setupTheme();
    }
    
    setupEventListeners() {
        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });
        
        // Global search
        const searchInput = document.getElementById('globalSearch');
        searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.performSearch(e.target.value);
            }, 300);
        });
        
        // Settings
        document.getElementById('saveSettings').addEventListener('click', () => {
            this.saveSettings();
        });
        
        // Directory scanner
        document.getElementById('startScan').addEventListener('click', () => {
            this.scanDirectory();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }
    
    async loadPreferences() {
        try {
            const response = await fetch('/api/preferences');
            const data = await response.json();
            this.preferences = data.preferences;
            this.applyPreferences();
        } catch (error) {
            console.error('Error loading preferences:', error);
            this.preferences = {
                theme: 'light',
                playback_speed: 1.0,
                auto_play_next: true,
                volume: 0.8,
                quality: 'auto'
            };
        }
    }
    
    applyPreferences() {
        // Apply theme
        document.body.setAttribute('data-bs-theme', this.preferences.theme);
        
        // Update settings form
        document.getElementById('playbackSpeed').value = this.preferences.playback_speed;
        document.getElementById('volume').value = this.preferences.volume;
        document.getElementById('autoPlayNext').checked = this.preferences.auto_play_next;
    }
    
    setupTheme() {
        const themeIcon = document.getElementById('themeIcon');
        const currentTheme = this.preferences.theme || 'light';
        
        if (currentTheme === 'dark') {
            themeIcon.className = 'bi bi-moon-fill';
        } else {
            themeIcon.className = 'bi bi-sun-fill';
        }
    }
    
    toggleTheme() {
        const currentTheme = document.body.getAttribute('data-bs-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        document.body.setAttribute('data-bs-theme', newTheme);
        this.preferences.theme = newTheme;
        
        const themeIcon = document.getElementById('themeIcon');
        themeIcon.className = newTheme === 'dark' ? 'bi bi-moon-fill' : 'bi bi-sun-fill';
        
        this.savePreferences();
    }
    
    async savePreferences() {
        try {
            await fetch('/api/preferences', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.preferences)
            });
        } catch (error) {
            console.error('Error saving preferences:', error);
        }
    }
    
    async saveSettings() {
        const playbackSpeed = parseFloat(document.getElementById('playbackSpeed').value);
        const volume = parseFloat(document.getElementById('volume').value);
        const autoPlayNext = document.getElementById('autoPlayNext').checked;
        
        this.preferences.playback_speed = playbackSpeed;
        this.preferences.volume = volume;
        this.preferences.auto_play_next = autoPlayNext;
        
        await this.savePreferences();
        
        // Apply to current player if exists
        if (this.player) {
            this.player.playbackRates([0.5, 0.75, 1, 1.25, 1.5, 2]);
            this.player.playbackRate(playbackSpeed);
            this.player.volume(volume);
        }
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
        modal.hide();
        
        this.showToast('Settings saved successfully', 'success');
    }
    
    async loadCourses() {
        try {
            const response = await fetch('/api/courses');
            const courses = await response.json();
            
            this.renderCourses(courses);
            this.updateStats(courses);
            
            if (courses.length === 0) {
                document.getElementById('welcomeSection').classList.remove('d-none');
                document.getElementById('courseContent').classList.add('d-none');
            }
        } catch (error) {
            console.error('Error loading courses:', error);
            this.showToast('Error loading courses', 'error');
        }
    }
    
    renderCourses(courses) {
        const coursesList = document.getElementById('coursesList');
        coursesList.innerHTML = '';
        
        if (courses.length === 0) {
            coursesList.innerHTML = `
                <div class="list-group-item text-center text-muted">
                    <i class="bi bi-folder-x display-6"></i>
                    <p class="mt-2 mb-0">No courses found</p>
                    <small>Add a course directory to get started</small>
                </div>
            `;
            return;
        }
        
        courses.forEach(course => {
            const courseItem = document.createElement('div');
            courseItem.className = 'list-group-item course-item';
            courseItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${course.name}</h6>
                        <small class="text-muted">${course.modules_count} modules</small>
                    </div>
                    <i class="bi bi-play-circle text-primary"></i>
                </div>
            `;
            
            courseItem.addEventListener('click', () => {
                this.loadCourse(course.id);
            });
            
            coursesList.appendChild(courseItem);
        });
    }
    
    updateStats(courses) {
        const totalCourses = courses.length;
        let totalVideos = 0;
        
        courses.forEach(course => {
            // This would need to be calculated properly from modules/videos
            totalVideos += course.modules_count * 5; // Rough estimate
        });
        
        document.getElementById('totalCourses').textContent = totalCourses;
        document.getElementById('totalVideos').textContent = totalVideos;
    }
    
    showToast(message, type = 'info') {
        const toast = document.getElementById('notificationToast');
        const toastMessage = document.getElementById('toastMessage');
        
        toastMessage.textContent = message;
        toast.className = `toast toast-${type}`;
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }
    
    handleKeyboardShortcuts(e) {
        // Only handle shortcuts when video player is active
        if (!this.player) return;
        
        switch(e.code) {
            case 'Space':
                e.preventDefault();
                if (this.player.paused()) {
                    this.player.play();
                } else {
                    this.player.pause();
                }
                break;
            case 'ArrowLeft':
                e.preventDefault();
                this.player.currentTime(this.player.currentTime() - 10);
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.player.currentTime(this.player.currentTime() + 10);
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.player.volume(Math.min(1, this.player.volume() + 0.1));
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.player.volume(Math.max(0, this.player.volume() - 0.1));
                break;
        }
    }
    
    formatDuration(seconds) {
        if (!seconds) return '0:00';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }
    
    formatFileSize(bytes) {
        if (!bytes) return '0 B';

        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));

        return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
    }

    async performSearch(query) {
        if (!query.trim()) {
            this.hideSearchResults();
            return;
        }

        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
            const results = await response.json();

            this.showSearchResults(results);
        } catch (error) {
            console.error('Error performing search:', error);
        }
    }

    showSearchResults(results) {
        // Remove existing search results
        this.hideSearchResults();

        const searchInput = document.getElementById('globalSearch');
        const searchContainer = searchInput.parentElement;

        const resultsDiv = document.createElement('div');
        resultsDiv.className = 'search-results';
        resultsDiv.id = 'searchResults';

        let hasResults = false;

        // Add courses
        if (results.courses && results.courses.length > 0) {
            hasResults = true;
            results.courses.forEach(course => {
                const item = document.createElement('div');
                item.className = 'search-result-item';
                item.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-folder-fill text-primary me-2"></i>
                        <div>
                            <div class="fw-bold">${course.name}</div>
                            <small class="text-muted">Course • ${course.modules_count} modules</small>
                        </div>
                    </div>
                `;
                item.addEventListener('click', () => {
                    this.loadCourse(course.id);
                    this.hideSearchResults();
                    searchInput.value = '';
                });
                resultsDiv.appendChild(item);
            });
        }

        // Add videos
        if (results.videos && results.videos.length > 0) {
            hasResults = true;
            results.videos.forEach(video => {
                const item = document.createElement('div');
                item.className = 'search-result-item';
                item.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-play-circle-fill text-success me-2"></i>
                        <div>
                            <div class="fw-bold">${video.name}</div>
                            <small class="text-muted">Video • ${this.formatDuration(video.duration)}</small>
                        </div>
                    </div>
                `;
                item.addEventListener('click', () => {
                    // This would need to load the course and play the specific video
                    this.hideSearchResults();
                    searchInput.value = '';
                });
                resultsDiv.appendChild(item);
            });
        }

        if (!hasResults) {
            const item = document.createElement('div');
            item.className = 'search-result-item text-center text-muted';
            item.innerHTML = 'No results found';
            resultsDiv.appendChild(item);
        }

        searchContainer.appendChild(resultsDiv);
    }

    hideSearchResults() {
        const existingResults = document.getElementById('searchResults');
        if (existingResults) {
            existingResults.remove();
        }
    }

    async scanDirectory() {
        const directoryPath = document.getElementById('directoryPath').value.trim();

        if (!directoryPath) {
            this.showToast('Please enter a directory path', 'error');
            return;
        }

        // Show progress
        document.getElementById('scanProgress').classList.remove('d-none');
        document.getElementById('scanResults').classList.add('d-none');
        document.getElementById('startScan').disabled = true;

        try {
            const response = await fetch('/api/scan-directory', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    path: directoryPath
                })
            });

            const result = await response.json();

            // Hide progress and show results
            document.getElementById('scanProgress').classList.add('d-none');
            document.getElementById('scanResults').classList.remove('d-none');

            const resultsList = document.getElementById('scanResultsList');
            resultsList.innerHTML = `
                <li><strong>Courses added:</strong> ${result.courses_added}</li>
                <li><strong>Modules added:</strong> ${result.modules_added}</li>
                <li><strong>Videos added:</strong> ${result.videos_added}</li>
            `;

            if (result.errors && result.errors.length > 0) {
                resultsList.innerHTML += '<li><strong>Errors:</strong><ul>';
                result.errors.forEach(error => {
                    resultsList.innerHTML += `<li class="text-danger">${error}</li>`;
                });
                resultsList.innerHTML += '</ul></li>';
            }

            // Reload courses
            this.loadCourses();

            this.showToast('Directory scan completed', 'success');

        } catch (error) {
            console.error('Error scanning directory:', error);
            this.showToast('Error scanning directory', 'error');

            document.getElementById('scanProgress').classList.add('d-none');
        } finally {
            document.getElementById('startScan').disabled = false;
        }
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    window.coursePlayer = new CoursePlayer();
});
